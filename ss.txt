{"success": true, "credits_left": 20, "rate_limit_left": 20, "person": {"publicIdentifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "linkedInIdentifier": "ACoAADF0g-sBEy7RaiWjGtmUm3UhPnZzExGu0-E", "memberIdentifier": "829719531", "linkedInUrl": "https://www.linkedin.com/in/uba<PERSON><PERSON><PERSON><PERSON>h", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "headline": "Top Rated Upwork Seller | Full Stack AI Engineer @ UBprogrammer.com | Tech Content Creator | One Man Agency | built 10+ SaaS from Idea to Product", "location": "Islāmābād, Pakistan", "summary": "Hey there! I am <PERSON><PERSON><PERSON>, and I call myself 'One Man Agency' and here is why:\n\n- I am a Top Rated Upwork Seller in the domain of Product Building (mostly AI related products) \n- I create Tech Content (mostly AI related content) on my YouTube Channel: @UBprogrammer\n- I have developed 10+ SaaS (from Idea to Product)\n- I do 'Full stack development' (NEXTjs, Django, Flask etc.)\n- I do 'no code' (Bubble, Make, Zapier etc.)\n- I do 'payment integrations' (PayPal, Stripe, etc.)\n- I do 'database integrations' (Firestore, Mongodb, etc.)\n- I develop 'AI solutions' from scratch and integrate it within existing workflows and products\n- I do 'LLM engineering' (OpenAI GPTs, Open source LLMs like LLAMA, mistral, On prem AI)\n- I do 'RAG Pipelines' (Vector Databases, FAISS, Local RAG development)\n- Finally, I do deployment of every service I build to production (Google, AWS (cloud run, cloud functions, storage buckets etc.))\n\nMy everyday work includes: \n- Generative AI: LLMs, OpenAI Products, LangChain, HuggingFace, GPT models, NLP, Vector dbs, etc.\n- Programming Languages: Python, JavaScript, Html, Css\n- Backend Frameworks (APIs): Django, Flask, FastAPI\n- Cloud & Deployment: AWS, GCP, Azure, Replit, Dockers, Kubernetes, Nginx\n- ML Model Building: TensorFlow, PyTorch, SkLearn\n- Data Handling: Cleaning, Wrangling, Visualizing, Story telling\n\nlooking forward to contributing to revolutionary projects.\nThanks!", "photoUrl": "https://media.licdn.com/dms/image/v2/D4D03AQF8ab_5mpRERw/profile-displayphoto-shrink_800_800/profile-displayphoto-shrink_800_800/0/1703430296301?e=1740009600&v=beta&t=BR1H-1Qy7_NUox8p-5nrIHn8XgyNS9lERu1dRxbMXeg", "backgroundUrl": "https://media.licdn.com/dms/image/v2/D4D16AQEkDcQ3-rRHTg/profile-displaybackgroundimage-shrink_350_1400/profile-displaybackgroundimage-shrink_350_1400/0/1704220838967?e=1740009600&v=beta&t=txRvg_2L-mmiCTOh5GLgDOj4E9biH_Xo4mfes-dsuIo", "openToWork": false, "premium": false, "pronoun": null, "showVerificationBadge": true, "creationDate": {"month": 7, "year": 2020}, "followerCount": 1050, "positions": {"positionsCount": 3, "positionHistory": [{"title": "Artificial Intelligence Instructor at UBprogrammer (YT Channel)", "companyName": "YouTube", "description": "- Provide guidance and consultancy services to subscribers seeking assistance with custom AI and ML projects as a freelance service.\n- Regularly produce high-quality videos related to AI, ML, DL, incorporating clear explanations, demonstrations and practical examples to effectively communicate complex concepts to a diverse audience.", "startEndDate": {"start": {"month": 1, "year": 2023}, "end": null}, "contractType": "Self-employed", "companyLogo": "https://media.licdn.com/dms/image/v2/C4D0BAQEfoRsyU4yUzg/company-logo_400_400/company-logo_400_400/0/1631053379845/youtube_logo?e=1743033600&v=beta&t=E_Ep3Qn7HLIgAvcQnxPBqIDshrRK55Zyz2TDEwxuK_k", "linkedInUrl": "https://www.linkedin.com/company/16140/", "linkedInId": "16140"}, {"title": "AI Engineer", "companyName": "Alt Ventures", "companyLocation": "Lahore, Punjab, Pakistan · Hybrid", "description": "- Developed end-to-end AI pipelines to address complex business challenges, optimizing data processing and model deployment workflows.\n- Collaborated with cross-functional teams to understand project requirements, ensuring alignment with business objectives and delivering possible AI solutions.\n- Proficiently used cutting-edge AI technologies, such as OpenAI Products (GPT models) and Langchain, to develop and deploy API and web applications.", "startEndDate": {"start": {"month": 2, "year": 2023}, "end": {"month": 3, "year": 2024}}, "contractType": "Full-time", "companyLogo": "https://media.licdn.com/dms/image/v2/D4D0BAQGwNhntGqAmjg/company-logo_400_400/company-logo_400_400/0/1732087775793/altventurespk_logo?e=1743033600&v=beta&t=ilfFPkWtoq9vKBXH-iWMfnFJwUGFLu3bRmlj_mTaoF8", "linkedInUrl": "https://www.linkedin.com/company/20375891/", "linkedInId": "20375891"}, {"title": "Machine Learning Engineer Intern", "companyName": "Bytewise Limited", "companyLocation": "Islāmābād, Pakistan · Remote", "description": "", "startEndDate": {"start": {"month": 3, "year": 2023}, "end": {"month": 6, "year": 2023}}, "contractType": "Internship", "companyLogo": "https://media.licdn.com/dms/image/v2/C4D0BAQHZALBWc0gcgA/company-logo_400_400/company-logo_400_400/0/1658601458794/thebytewise_logo?e=1743033600&v=beta&t=kr7t9EPr5CK4OOHrafCCSPbPAvgwifISGSbGbqt0a0M", "linkedInUrl": "https://www.linkedin.com/company/30733578/", "linkedInId": "30733578"}]}, "schools": {"educationsCount": 3, "educationHistory": [{"degreeName": "Bachelor's degree", "fieldOfStudy": "Computer Software Engineering", "description": "Skills: Pandas (Software) · Front-End Development · Seaborn · SQL", "linkedInUrl": "https://www.linkedin.com/company/511293/", "schoolLogo": "https://media.licdn.com/dms/image/v2/D4D0BAQE4u0dT2bemIg/company-logo_400_400/company-logo_400_400/0/1663664045761/virtual_university_of_pakistan_logo?e=1743033600&v=beta&t=_JFIhDVHOjhuAxEG5L7NGRvPVNvne46ShAOeYNBX0wM", "schoolName": "Virtual University of Pakistan", "startEndDate": {"start": {"month": 1, "year": 2020}, "end": {"month": 1, "year": 2024}}}, {"degreeName": "AI Programming with Python (Nanodegree)", "fieldOfStudy": null, "linkedInUrl": "https://www.linkedin.com/company/2475568/", "schoolLogo": "https://media.licdn.com/dms/image/v2/C560BAQHiNYfm0YHKrg/company-logo_400_400/company-logo_400_400/0/1656621848677/udacity_logo?e=1743033600&v=beta&t=m7XYS0yeeBQRT6RVRvZoZtqSSH4ArO2WcwN3R8lLa9Y", "schoolName": "Udacity", "startEndDate": {"start": {"month": 9, "year": 2022}, "end": {"month": 2, "year": 2023}}}, {"degreeName": "Data Science Bootcamp (Diploma - 6 Months)", "fieldOfStudy": null, "linkedInUrl": "https://www.linkedin.com/search/results/all/?keywords=National+Vocational+and+Technical+Training+Commission+NAVTTC", "schoolLogo": null, "schoolName": "National Vocational and Technical Training Commission NAVTTC", "startEndDate": {"start": {"month": 3, "year": 2021}, "end": {"month": 9, "year": 2021}}}]}, "skills": ["JavaScript", "Docker Products", "Back-End Web Development", "Google Cloud Platform (GCP)", "Amazon Web Services (AWS)", "Natural Language Processing (NLP)", "Large Language Models (LLM)", "Communication", "Project Delivery", "Freelancing", "Spoken English", "ChatGPT", "OpenAI Products", "SQL", "Statistical Data Analysis", "Data Mining", "Probability", "Data Analysis", "SciPy", "Data Analytics", "<PERSON><PERSON>", "Pandas (Software)", "Front-End Development", "NumPy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Scikit-Learn", "Deep Learning", "RStudio", "TensorFlow", "<PERSON><PERSON>", "PyTorch", "Django", "Flask", "Machine Learning", "Artificial Intelligence (AI)", "Data Science", "Neural Networks", "Python (Programming Language)", "R (Programming Language)", "Blockchain"], "languages": [], "recommendations": {"recommendationsCount": 6, "recommendationHistory": [{"caption": "April 30, 2024, <PERSON> was <PERSON><PERSON><PERSON>’s client", "description": "<PERSON><PERSON><PERSON>'s expertise in AI and as a full stack developer has been extremely helpful for our team. He's particularly strong in scoping out our requirements in delivering in a timely manner.", "authorFullname": "<PERSON>", "authorUrl": "https://www.linkedin.com/in/nosyarg"}, {"caption": "March 23, 2024, <PERSON><PERSON> was <PERSON><PERSON><PERSON>’s client", "description": "He is very talented. He helped me a lot in a project. He is really a very capable and creative person.", "authorFullname": "<PERSON><PERSON>", "authorUrl": "https://www.linkedin.com/in/dinara-abbasli-108821217"}, {"caption": "March 5, 2024, <PERSON><PERSON><PERSON> was <PERSON><PERSON><PERSON>’s client", "description": "In this difficult area in terms of labor, <PERSON><PERSON><PERSON> is a pleasant surprise: a serious professional, commited to the customer's success, very technical and with a rare business and entepreneurial vision. We are very pleased with his work and hope to produce more intereasting things together.", "authorFullname": "Aislan Y.", "authorUrl": "https://www.linkedin.com/in/aislan<PERSON>er"}, {"caption": "December 6, 2023, <PERSON> was <PERSON><PERSON><PERSON>’s client", "description": "I highly recommend <PERSON><PERSON><PERSON> for his exceptional blend of soft skills and technical expertise. <PERSON><PERSON><PERSON>'s proficiency in Google Cloud and machine learning is outstanding, showcasing not only a deep understanding of complex techs but also an ability to apply them effectively. Moreover, his perseverance ensures he tackles challenging tasks with dedication and a problem-solving mindset. <PERSON><PERSON><PERSON>'s interpersonal skills make him a valuable team player, enhancing collaborative efforts. His blend of technical know-how and soft skills make him an asset to any team or client in the tech industry.", "authorFullname": "<PERSON>", "authorUrl": "https://www.linkedin.com/in/andre-gazineu-75031b228"}, {"caption": "September 23, 2023, <PERSON> managed <PERSON><PERSON><PERSON> directly", "description": "I highly recommend <PERSON><PERSON><PERSON> for his exceptional AI engineering skills and experience. His ability to build innovative solutions for complex problems is truly remarkable. <PERSON><PERSON><PERSON>'s proactive approach ensures projects are not only delivered on time but also exceed expectations. An invaluable asset to any team!", "authorFullname": "<PERSON>", "authorUrl": "https://www.linkedin.com/in/muham<PERSON><PERSON><PERSON>"}, {"caption": "July 11, 2023, <PERSON><PERSON><PERSON> worked with <PERSON><PERSON><PERSON> on the same team", "description": "<PERSON><PERSON><PERSON> joined AltFind as an AI intern and it has been an absolute pleasure working with him. I was amazed by his passion for learning and taking on challenges from scratch. In just a few weeks, he made significant contributions to our product by helping build important AI integrations. I highly recommend <PERSON><PERSON><PERSON> for his exceptional developmental skills, teamwork, and commitment to excellence.", "authorFullname": "<PERSON><PERSON><PERSON>", "authorUrl": "https://www.linkedin.com/in/urooj-haider-754a731b6"}]}, "certifications": {"certificationsCount": 20, "certificationHistory": [{"name": "Generative AI Fundamentals", "organizationName": "Google", "organizationUrl": null, "issuedDate": "Issued Aug 2023"}, {"name": "Introduction to Generative AI", "organizationName": "Google", "organizationUrl": null, "issuedDate": "Issued Aug 2023"}, {"name": "Introduction to Large Language Models", "organizationName": "Google", "organizationUrl": "https://www.linkedin.com/company/1441/", "issuedDate": "Issued Aug 2023"}, {"name": "Introduction to Responsible AI", "organizationName": "Google", "organizationUrl": "https://www.linkedin.com/company/1441/", "issuedDate": "Issued Aug 2023"}, {"name": "ML Engineer Internship", "organizationName": "Bytewise Limited", "organizationUrl": "https://www.linkedin.com/company/30733578/", "issuedDate": "Issued Feb 2023"}, {"name": "Data Analysis with Python", "organizationName": "freeCodeCamp", "organizationUrl": "https://www.linkedin.com/company/4831032/", "issuedDate": "Issued Feb 2022"}, {"name": "Python Skill Assessment - HackerRank", "organizationName": "HackerRank", "organizationUrl": "https://www.linkedin.com/company/435210/", "issuedDate": "Issued Dec 2021"}, {"name": "IBM - Data Analytics", "organizationName": "Coursera", "organizationUrl": "https://www.linkedin.com/company/2453129/", "issuedDate": "Issued Jun 2021"}, {"name": "The Data Scientist’s Toolbox", "organizationName": "Coursera", "organizationUrl": "https://www.linkedin.com/company/2453129/", "issuedDate": "Issued Mar 2019"}, {"name": "Ask Questions to Make Data-Driven Decisions", "organizationName": "Coursera", "organizationUrl": "https://www.linkedin.com/company/2453129/", "issuedDate": null}, {"name": "Cloud Computing Basics (Cloud 101)", "organizationName": "LearnQuest", "organizationUrl": "https://www.linkedin.com/company/39131/", "issuedDate": null}, {"name": "Digital Skills: Artificial Intelligence ", "organizationName": "Accenture", "organizationUrl": "https://www.linkedin.com/company/1033/", "issuedDate": null}, {"name": "English Writing - 1", "organizationName": "King's College London", "organizationUrl": "https://www.linkedin.com/company/7198/", "issuedDate": null}, {"name": "English Writing - 2", "organizationName": "King's College London", "organizationUrl": "https://www.linkedin.com/company/7198/", "issuedDate": null}, {"name": "English Writing - 3", "organizationName": "King's College London", "organizationUrl": "https://www.linkedin.com/company/7198/", "issuedDate": null}, {"name": "Foundations: Data, Data, Everywhere", "organizationName": "Coursera", "organizationUrl": "https://www.linkedin.com/company/2453129/", "issuedDate": null}, {"name": "Introduction to SQL", "organizationName": "DataCamp", "organizationUrl": "https://www.linkedin.com/company/3227175/", "issuedDate": null}, {"name": "PROFESSIONAL RESILIENCE: BUILDING SKILLS TO THRIVE AT WORK", "organizationName": "Deakin University", "organizationUrl": "https://www.linkedin.com/company/11710/", "issuedDate": null}, {"name": "Prepare Data for Exploration", "organizationName": "Coursera", "organizationUrl": "https://www.linkedin.com/company/2453129/", "issuedDate": null}, {"name": "Process Data from Dirty to Clean", "organizationName": "Coursera", "organizationUrl": "https://www.linkedin.com/company/2453129/", "issuedDate": null}]}}, "company": {"linkedInId": "16140", "name": "YouTube", "universalName": "youtube", "linkedInUrl": "https://www.linkedin.com/company/16140", "employeeCount": 119843, "followerCount": 2193953, "employeeCountRange": {"start": 1001, "end": 5000}, "websiteUrl": "http://www.youtube.com/jobs", "tagline": "Like and Subscribe", "description": "YouTube is a team-oriented, creative workplace where every single employee has a voice in the choices we make and the features we implement. We work together in small teams to design, develop, and roll out key features and products in very short time frames. Which means something you write today could be seen by millions of viewers tomorrow. And even though we’re a part of Google and enjoy all of the corporate perks, it still feels like you’re working at a startup.\n\nYouTube is headquartered in San Bruno, California, 12 miles south of San Francisco. We also have many job openings in Mountain View, and across the globe", "industry": "Technology, Information and Internet", "phone": null, "specialities": [], "headquarter": {"city": "San Bruno", "country": "US", "postalCode": "94066", "geographicArea": "CA", "street1": "901 Cherry Ave.", "street2": null}, "logo": "https://media.licdn.com/dms/image/v2/C4D0BAQEfoRsyU4yUzg/company-logo_400_400/company-logo_400_400/0/1631053379845/youtube_logo?e=1743033600&v=beta&t=E_Ep3Qn7HLIgAvcQnxPBqIDshrRK55Zyz2TDEwxuK_k", "fundingData": {"numberOfFundingRounds": 2, "crunchbaseOrganizationUrl": "https://www.crunchbase.com/organization/youtube?utm_source=linkedin&utm_medium=referral&utm_campaign=linkedin_companies&utm_content=profile_cta", "lastFundingRound": {"fundingType": "Series unknown", "moneyRaised": {"amount": "8000000", "currencyCode": "USD"}, "fundingRoundUrl": "https://www.crunchbase.com/funding_round/youtube-series-unknown--f148ced9?utm_source=linkedin&utm_medium=referral&utm_campaign=linkedin_companies&utm_content=last_funding", "announcedOn": "2006-03-30T00:00:00.000Z", "numberOfOtherInvestors": 0, "investorsUrl": "https://www.crunchbase.com/funding_round/youtube-series-unknown--f148ced9?utm_source=linkedin&utm_medium=referral&utm_campaign=linkedin_companies&utm_content=all_investors", "leadInvestors": [{"name": "Sequoia Capital", "url": "https://www.crunchbase.com/organization/sequoia-capital?utm_source=linkedin&utm_medium=referral&utm_campaign=linkedin_companies&utm_content=investor", "image": "https://media.licdn.com/dms/image/sync/v2/D4D38AQEWDIx8weWWZQ/crunchbase_investor_logo_100/crunchbase_investor_logo_100/0/1733942495342?e=1735419600&v=beta&t=WDz3kG-Js8B6ffNuE4izbAao6Btz-xDTjzRTNZkdRlg"}, {"name": "Artis Ventures (AV)", "url": "https://www.crunchbase.com/organization/artis?utm_source=linkedin&utm_medium=referral&utm_campaign=linkedin_companies&utm_content=investor", "image": "https://media.licdn.com/dms/image/sync/v2/D4D38AQGmJur75rleCQ/crunchbase_investor_logo_100/crunchbase_investor_logo_100/0/1733942495705?e=1735419600&v=beta&t=U93f8UuJ_RszewwmnjyNCV86ea7aMP5Y1v1VvERGyX4"}]}}}}