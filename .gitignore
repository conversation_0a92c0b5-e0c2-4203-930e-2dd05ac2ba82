# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# API keys and credentials
stripe_keys.txt
*.json
!package.json
!package-lock.json
!tsconfig.json
!jsconfig.json
!tailwind.config.js
!next.config.mjs
!postcss.config.mjs

# Python notebooks with potential secrets
*.ipynb
/package-lock.json
/backend/package-lock.json
