{"name": "pink<PERSON>y-backend", "version": "1.0.0", "description": "Express.js backend for Pink Honey AI Companion App", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1", "seed": "node scripts/seed.js"}, "dependencies": {"@clerk/backend": "^1.32.2", "@clerk/express": "^1.4.19", "axios": "^1.6.7", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "firebase-admin": "^12.0.0", "form-data": "^4.0.0", "helmet": "^7.1.0", "joi": "^17.12.2", "mongodb": "^6.16.0", "mongoose": "^8.15.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "openai": "^4.28.4", "stripe": "^14.18.0", "winston": "^3.11.0"}, "devDependencies": {"eslint": "^8.57.0", "nodemon": "^3.0.3"}, "engines": {"node": ">=18.0.0"}}