# ==========================
# 🌐 Server Configuration
# ==========================
PORT=8080
NODE_ENV=development
FRONTEND_URL=http://localhost:3000
JWT_SECRET=your_jwt_secret_here

# ==========================
# 🔐 API Keys
# ==========================
OPENAI_API_KEY=your_openai_api_key_here

# ==========================
# 🧾 Stripe Configuration
# ==========================
STRIPE_SECRET_KEY=your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here

# 💳 Stripe Price IDs
STRIPE_DEFAULT_PRICE_ID=price_default_id_here
STRIPE_LIFETIME_PRICE_ID=price_lifetime_id_here
STRIPE_YEARLY_PRICE_ID=price_yearly_id_here
STRIPE_MONTHLY_PRICE_ID=price_monthly_id_here

# ==========================
# 🔐 Clerk Authentication
# ==========================
CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key_here
CLERK_SECRET_KEY=your_clerk_secret_key_here
CLERK_WEBHOOK_SECRET=your_clerk_webhook_secret_here

# ==========================
# 🗃️ MongoDB Configuration
# ==========================
MONGODB_URI=mongodb://localhost:27017/pinkhoney


